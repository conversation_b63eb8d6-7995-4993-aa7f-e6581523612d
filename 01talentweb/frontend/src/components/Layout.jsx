import { Link } from "@inertiajs/react";
import Navbar from "@/components/Navbar.jsx";
import Footer from "@/components/Footer.jsx";

// Create a container component for consistent spacing with fluid margins
export const Container = ({ children, className = "" }) => (
  <div className={`mx-auto px-fluid-sm max-w-7xl ${className}`}>
    {children}
  </div>
);

const Layout = ({children}) => (
<>
<main className="w-full h-screen overflow-hidden overflow-y-scroll">
       <Navbar />
       { children }
       <Footer />
</main>

</>
)

export default page => <Layout>{page}</Layout>;