import React from 'react';
import { Container } from '../../../components/Layout.jsx';

const NewsletterSection = () => {
  return (
    <section className="w-full bg-[--color-primary-50] py-fluid-lg">
      <Container className="text-center">
        <h2 className="text-fluid-h1 font-bold text-[var(--color-text-heading)] mb-fluid-sm leading-[1.2]">
          Subscribe to our{' '}
          <span className="text-[--color-primary-500]">Monthly Newsletter</span>
        </h2>
        <p className="text-fluid-body-l pb-fluid-lg leading-[1.5] font-normal text-[var(--color-text-muted)]">
          Malesuada ut aliquam at ac est nisi, interdum etiam dignissim.
        </p>

        {/* Enhanced form with custom CSS classes for optimal fluid behavior */}
        <form className="newsletter-form">
          <div className="relative w-full">
            <input
              type="email"
              placeholder="Enter your email"
              className="newsletter-input placeholder:text-[var(--color-text-muted)]"
              required
              aria-label="Email address"
            />

            <button
              type="submit"
              variant="filled"
              className="newsletter-button"
              aria-label="Subscribe to newsletter"
            >
              Subscribe
            </button>
          </div>
        </form>

      </Container>
    </section>
  );
};

export default NewsletterSection;
