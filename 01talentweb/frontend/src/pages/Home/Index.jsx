import React from 'react';
import { usePage } from '@inertiajs/react';
import Navbar from '../../components/Navbar.jsx';
import Hero from '../../components/Hero.jsx';
import Stats from './components/Stats.jsx';
import CompanyLogos from './components/CompanyLogos.jsx';
import UniqueTalentSection from './components/UniqueTalent.jsx';
import FeaturedProfiles from './components/FeaturedProfiles.jsx';
import Newsletter from './components/Newsletter.jsx';
import WhoWeAre from './components/WhoWeAre.jsx';
import HiringModel from './components/HiringModel.jsx';
import HiringModelMobile from './components/HiringModelMobile.jsx';
import Button from '../../components/Button.jsx';



import WhatsMoreSection from './components/Whatsmore.jsx';


export const SampleTalents = [
    { 
      id: 1,
      name: '<PERSON><PERSON>',
      role: 'Web Developer',
      image: '/images/talents/joeylene.jpg',
      skills: ['Golang', 'Docker', 'RESTful APIs', 'Database Design'],
      average_rating: 4.8,
    },
    {
      id: 2,
      name: '<PERSON>a Njeri',
      role: 'Data Analyst',
      image: '/images/talents/amara.jpg',
      skills: ['Python', 'Data Analysis', 'Pandas', 'Jupyter'],
      average_rating: 4.5,
    },
    {
      id: 3,
      name: 'Ezekiel Mwangi',
      role: 'Full Stack Developer',
      image: '/images/talents/ezekiel.jpg',
      skills: ['React', 'Tailwind CSS', 'Next.js', 'TypeScript'],
      average_rating: 4.9,
    },
    {
      id: 4,
      name: 'Fatima Khalid',
      role: 'UX Designer',
      image: '/images/talents/fatima.jpg',
      skills: ['UX Design', 'Figma', 'Accessibility', 'Design Systems'],
      average_rating: 4.6,
    },
    {
      id: 5,
      name: 'Brian Ochieng',
      role: 'Backend Engineer',
      image: '/images/talents/brian.jpg',
      skills: ['Java', 'Spring Boot', 'MySQL', 'APIs'],
      average_rating: 4.7,
    },
    {
      id: 6,
      name: 'Zainab Yusuf',
      role: 'Cybersecurity Engineer',
      image: '/images/talents/zainab.jpg',
      skills: ['Cybersecurity', 'Linux', 'Networking', 'Ethical Hacking'],
      average_rating: 4.4,
    },
    { 
      id: 7,
      name: 'Joeylene Rivera',
      role: 'Web Developer',
      image: '/images/talents/joeylene.jpg',
      skills: ['Golang', 'Docker', 'RESTful APIs', 'Database Design'],
      average_rating: 4.8,
    },
    {
      id: 8,
      name: 'Amara Njeri',
      role: 'Data Analyst',
      image: '/images/talents/amara.jpg',
      skills: ['Python', 'Data Analysis', 'Pandas', 'Jupyter'],
      average_rating: 4.5,
    },
    {
      id: 9,
      name: 'Ezekiel Mwangi',
      role: 'Full Stack Developer',
      image: '/images/talents/ezekiel.jpg',
      skills: ['React', 'Tailwind CSS', 'Next.js', 'TypeScript'],
      average_rating: 4.9,
    },
    {
      id: 10,
      name: 'Fatima Khalid',
      role: 'UX Designer',
      image: '/images/talents/fatima.jpg',
      skills: ['UX Design', 'Figma', 'Accessibility', 'Design Systems'],
      average_rating: 4.6,
    },
    {
      id: 11,
      name: 'Brian Ochieng',
      role: 'Backend Engineer',
      image: '/images/talents/brian.jpg',
      skills: ['Java', 'Spring Boot', 'MySQL', 'APIs'],
      average_rating: 4.7,
    },
    {
      id: 12,
      name: 'Zainab Yusuf',
      role: 'Cybersecurity Engineer',
      image: '/images/talents/zainab.jpg',
      skills: ['Cybersecurity', 'Linux', 'Networking', 'Ethical Hacking'],
      average_rating: 4.4,
    },
  ];

  



// import './App.css'; // for animation styling
export default function Index() {
    const { props } = usePage();
    const featured_developers = props.featured_developers || [];

    return (
      <>
       <Hero
        desktopBg="/static/images/hero_team_desktop.JPG"
        mobileBg="/static/images/hero_team_mobile.JPG"
        title={
                <>
                  PROVIDING YOU WITH THE <span className="text-[--color-hero-text] ">BEST TECH TALENT</span> IN KENYA
                </>
              }
        description="We are a software talent agency that is addressing the global need for top tech talent by transforming access to high-tech jobs."
        button={
          <Button
            onClick={() => {}}
            style={{
              color: '#FFFFFF',
              padding: '22.92px 91.69px',
              width: '100%',
              maxWidth: '377.24px',
              height: '75.69px',
            }}
            className="hover:bg-[#284B81] bg-[var(--color-primary-300)] transition-colors duration-300 md:text-xl lg:text-2xl xl:text-3xl"
          >
            Hire Here
          </Button>
        }          
      />
        {/* <Hero /> */}
        <Stats />
        <CompanyLogos />
        <WhoWeAre />
        <div className="hidden md:block">
          <HiringModel />
        </div>

        {/* Mobile version: only visible on small screens */}
        <div className="block md:hidden">
          <HiringModelMobile />
        </div>

        <WhatsMoreSection/>
        <UniqueTalentSection/>
        <FeaturedProfiles talents={SampleTalents} />
        <Newsletter />
      </>
    );
}