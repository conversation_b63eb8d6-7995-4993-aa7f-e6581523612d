/** @type {import('tailwindcss').Config} */
module.exports = {
  mode: 'jit',
  content: [
    "./src/**/*.{js,jsx,ts,tsx}",
    "./backend/templates/**/*.{html,js,jsx,ts,tsx}",
  ],
  theme: {
    extend: {
      fontSize: {
        'h0': 'var(--text-h0)',
        'h1': 'var(--text-h1)',
        'h2': 'var(--text-h2)',
        'mobile-h1': 'var(--text-mobile-h1)',
        'mobile-h2': 'var(--text-mobile-h2)',
        'body-xxl': 'var(--text-body-xxl)',
        'body-xl': 'var(--text-body-xl)',
        'body-l': 'var(--text-body-l)',
        'body-m': 'var(--text-body-m)',
        'body-xs': 'var(--text-body-xs)',
        'body-s': 'var(--text-body-s)',

        // Fluid typography
        'fluid-h1': 'var(--text-fluid-h1)',
        'fluid-h2': 'var(--text-fluid-h2)',
        'fluid-h3': 'var(--text-fluid-h3)',
        'fluid-body-xl': 'var(--text-fluid-body-xl)',
        'fluid-body-l': 'var(--text-fluid-body-l)',
        'fluid-body-m': 'var(--text-fluid-body-m)',
      },
      lineHeight: {
        'h1': '1.3',
        'h2': '1.04',
        'mobile-headlines': '1.44',
        'body-l': '1.44',
        'body-m': '1.32',
      },
      screens: {
        // Base breakpoints
        'xxs': '320px',
        'xs': '475px',
        'sm': '320px',
        's': '525px',
        'md': '768px',
        'lg': '1024px',
        'xl': '1280px',
        '2xl': '2000px',
        
        // Responsive ranges
        'small-phones': { 'max': '424px' },  // 0-424px
        'large-phones': { 'min': '425px', 'max': '767px' },  // 425px-767px
        'small-tablets': { 'min': '768px', 'max': '1023px' }, // 768px-1023px
        'mid-tablets': { 'min': '1024px', 'max': '1279px' }, // 1024px-1279px
        'large-tablets': { 'min': '1280px', 'max': '1535px' }, // 1280px-1535px
        'desktop': { 'min': '1536px' },  // 1536px and up
        
        // Additional breakpoints
        'ultra-large': { 'min': '1710px' },  // 1710px and up
        'ultra-xl': { 'min': '1932px' },     // 1932px and up
      },
      fontFamily: {
        sans: ['var(--font-sans)'],
        roboto: ['var(--font-roboto)'],
      },
      aspectRatio: {
        'form-button': '4 / 1',
        'form-button-narrow': '3 / 1',
        'card': '4 / 5',
        'hero': '16 / 9',
      },
      spacing: {
        'fluid-xs': 'var(--space-fluid-xs)',
        'fluid-sm': 'var(--space-fluid-sm)',
        'fluid-md': 'var(--space-fluid-md)',
        'fluid-lg': 'var(--space-fluid-lg)',
        'fluid-xl': 'var(--space-fluid-xl)',
      },
      height: {
        'input-fluid': 'var(--input-height-fluid)',
        'button-fluid': 'var(--button-height-fluid)',
      },
      transitionDuration: {
        '600': '600ms',
      },
    },
  },
  plugins: [
  //    function ({ addUtilities }) {
  //   addUtilities({
  //     '.scrollbar-none': {
  //       '-ms-overflow-style': 'none',
  //       'scrollbar-width': 'none',
  //     },
  //     '.scrollbar-none::-webkit-scrollbar': {
  //       display: 'none',
  //     },
  //   });
  // },
  ],
}