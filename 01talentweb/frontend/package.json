{"name": "dirt", "version": "1.0.0", "description": "", "scripts": {"vite-build": "vite build", "vite-dev": "vite", "tailwind-dev": "tailwindcss -i ./static/css/main.css -o ./static/dist/css/app.css --watch", "dirt-dev": "concurrently \"npm run tailwind-dev\" \"npm run vite-dev\" ", "test": "echo \"Error: no test specified\" && exit 1"}, "author": "", "license": "ISC", "devDependencies": {"@inertiajs/react": "^1.0.8", "@vitejs/plugin-react": "^4.0.0", "autoprefixer": "^10.4.21", "axios": "^1.4.0", "concurrently": "^8.2.2", "postcss": "^8.5.3", "prettier": "^2.8.8", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwindcss": "^3.4.17", "vite": "^4.3.9"}, "dependencies": {"lucide-react": "^0.511.0", "react-icons": "^5.5.0", "react-responsive-carousel": "^3.2.23"}}