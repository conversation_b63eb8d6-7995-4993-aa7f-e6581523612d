# Running postgres using downloaded binaries

## starting postgres manually
```bash
$HOME/pgsql/bin/pg_ctl -D $HOME/pgsql/data start
```

## connect with psql
```bash
$HOME/pgsql/bin/psql -U rotieno -d postgres
```
# Run postgres on windows
```bash
$psql -h <hostname> -U <user> -d <database>
```

## Installing react icons e.g search icon
```bash
npm install react-icons
```
## Installing carousel
```bash
npm install react-responsive-carousel
```
