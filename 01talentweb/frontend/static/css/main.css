@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Typography */
    --font-sans: 'Montserrat', system-ui, -apple-system, sans-serif;
    
    /* Font Weights */
    --font-light: 300;
    --font-normal: 400;
    --font-medium: 500;
    --font-semibold: 600;
    --font-bold: 700;
    --font-extrabold: 800;
    
    /* Font Sizes (from design system) */
    /* Desktop Headlines */
    --text-h0: 38px;
    --text-h1: 64px;        /* 64px */
    --text-h2: 56px;      /* 56px */

    /* Mobile Headlines */
    --text-mobile-h1: 40px;  /* 40px */
    --text-mobile-h2: 13px;    /* 13px */

    /* Body Text */
    --text-body-xxl: 36px;
    --text-body-xl: 28px;   /* 28px */
    --text-body-l: 24px;
    --text-body-m: 20px;   /* 20px */
    --text-body-xs: 18px;
    --text-body-s: 16px;       /* 16px */

    /* Fluid Typography - Smooth scaling between breakpoints */
    --text-fluid-h1: clamp(2.5rem, 4vw + 1rem, 4rem);        /* 40px - 64px */
    --text-fluid-h2: clamp(2rem, 3.5vw + 0.5rem, 3.5rem);    /* 32px - 56px */
    --text-fluid-h3: clamp(1.75rem, 3vw + 0.5rem, 3rem);     /* 28px - 48px */
    --text-fluid-body-xl: clamp(1.125rem, 2vw + 0.5rem, 1.75rem); /* 18px - 28px */
    --text-fluid-body-l: clamp(1rem, 1.5vw + 0.5rem, 1.5rem);     /* 16px - 24px */
    --text-fluid-body-m: clamp(0.875rem, 1.25vw + 0.5rem, 1.25rem); /* 14px - 20px */
    
    /* Line Heights */
    --leading-h1: 1.3;       /* 130% */
    --leading-h2: 1.04;      /* 104% */
    --leading-mobile-headlines: 1.44; /* 72% */
    --leading-body-l: 1.44;  /* 144% */
    --leading-body-m: 1.32;  /* 132% */
    --leading-normal: 1.5;
    
    /* Spacing Scale (8px base) */
    --space-0: 0;
    --space-1: 0.5rem;      /* 8px */
    --space-2: 1rem;        /* 16px */
    --space-3: 1.5rem;      /* 24px */
    --space-4: 2rem;        /* 32px */
    --space-5: 2.5rem;      /* 40px */
    --space-6: 3rem;        /* 48px */
    --space-8: 4rem;        /* 64px */

    /* Fluid Spacing - Smooth scaling between breakpoints */
    --space-fluid-xs: clamp(0.5rem, 1vw, 1rem);      /* 8px - 16px */
    --space-fluid-sm: clamp(1rem, 2vw, 2rem);        /* 16px - 32px */
    --space-fluid-md: clamp(1.5rem, 3vw, 3rem);      /* 24px - 48px */
    --space-fluid-lg: clamp(2rem, 4vw, 4rem);        /* 32px - 64px */
    --space-fluid-xl: clamp(3rem, 6vw, 6rem);        /* 48px - 96px */

    /* Form Element Fluid Sizing */
    --input-height-fluid: clamp(3rem, 5vw + 1rem, 6.25rem);  /* 48px - 100px */
    --button-height-fluid: clamp(2.5rem, 4vw + 0.5rem, 4rem); /* 40px - 64px */
    
    /* Color Palette - Primary Blues */
    --color-primary-0: #FFFFFF;
    --color-primary-50: #EEF7FF;
    --color-primary-100: #3385FF;
    --color-primary-200: #1F78FF;
    --color-primary-300: #0063F9;
    --color-primary-400: #005AED;
    --color-primary-500: #0052CC;  /* Primary brand color */
    --color-primary-600: #2B5290;
    --color-primary-700: #284B81;
    --color-primary-800: #000000;
    --color-primary-button-600: #0063F9; 
    --color-hero-text: #005AE0;  
    --border-color: #0A142F;  
    
    /* Color Palette - Informational */
    --color-error: #EA191D;
    --color-warning: #FF9500;
    --color-success: #02DADB;
    --color-info: #0559DA;
    
    /* Semantic Colors */
    --color-text-heading: #142C5E; 
    --color-text: #0f172a;       /* Very dark blue/black */
    --color-text-muted: #64748b;
    --color-bg: #ffffff;
    --color-bg-muted: #f8fafc;
    --color-border: #e2e8f0;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 82 204 / 0.05);
    --shadow: 0 1px 3px 0 rgb(0 82 204 / 0.1), 0 1px 2px -1px rgb(0 82 204 / 0.1);
    --shadow-md: 0 4px 6px -1px rgb(0 82 204 / 0.1), 0 2px 4px -2px rgb(0 82 204 / 0.1);
    
    /* Border Radius */
    --radius-sm: 0.125rem;   /* 2px */
    --radius: 0.25rem;       /* 4px */
    --radius-md: 0.375rem;   /* 6px */
    --radius-lg: 0.5rem;     /* 8px */
  }
  
  /* Responsive typography */
  @screen md {
    :root {
      --text-mobile-h1: var(--text-h1);
      --text-mobile-h2: var(--text-h2);
    }
  }
}